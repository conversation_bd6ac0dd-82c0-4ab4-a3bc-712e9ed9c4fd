import subprocess
import socket
import os
import sys
import time

def check_port_usage(port):
    """Check what's using a specific port"""
    try:
        result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        
        for line in lines:
            if f':{port}' in line and 'LISTENING' in line:
                parts = line.split()
                if len(parts) >= 5:
                    pid = parts[-1]
                    return pid
        return None
    except Exception as e:
        print(f"Error checking port usage: {e}")
        return None

def get_process_name(pid):
    """Get process name from PID"""
    try:
        result = subprocess.run(['tasklist', '/FI', f'PID eq {pid}'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        for line in lines:
            if pid in line:
                parts = line.split()
                if len(parts) > 0:
                    return parts[0]
        return "Unknown"
    except Exception as e:
        return f"Error: {e}"

def kill_process_on_port(port):
    """Kill process using a specific port"""
    pid = check_port_usage(port)
    if pid:
        process_name = get_process_name(pid)
        print(f"Port {port} is being used by PID {pid} ({process_name})")
        
        try:
            subprocess.run(['taskkill', '/PID', pid, '/F'], check=True)
            print(f"✅ Killed process {pid} ({process_name})")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to kill process {pid}: {e}")
            return False
    else:
        print(f"Port {port} is not in use")
        return True

def check_xampp_services():
    """Check XAMPP service status"""
    print("🔍 Checking XAMPP Services Status")
    print("-" * 50)
    
    # Check common ports
    ports_to_check = {
        80: "Apache HTTP",
        443: "Apache HTTPS", 
        3306: "MySQL"
    }
    
    for port, service in ports_to_check.items():
        print(f"\n📡 Checking port {port} ({service})...")
        
        # Check if port is open
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print(f"✅ Port {port} is open")
                pid = check_port_usage(port)
                if pid:
                    process_name = get_process_name(pid)
                    print(f"   Used by: PID {pid} ({process_name})")
            else:
                print(f"❌ Port {port} is not accessible")
                
        except Exception as e:
            print(f"❌ Error checking port {port}: {e}")

def fix_apache_port_conflicts():
    """Fix Apache port conflicts"""
    print("\n🔧 Fixing Apache Port Conflicts")
    print("-" * 50)
    
    # Common conflicting processes on port 80
    conflicting_processes = ['w3wp.exe', 'iisexpress.exe', 'httpd.exe', 'nginx.exe']
    
    # Check port 80
    pid_80 = check_port_usage(80)
    if pid_80:
        process_name = get_process_name(pid_80)
        print(f"Port 80 is used by: {process_name} (PID: {pid_80})")
        
        if process_name.lower() in [p.lower() for p in conflicting_processes]:
            print(f"⚠️  {process_name} is likely conflicting with Apache")
            response = input(f"Do you want to kill {process_name}? (y/n): ")
            if response.lower() == 'y':
                kill_process_on_port(80)
        else:
            print(f"Unknown process using port 80: {process_name}")
    
    # Check port 443
    pid_443 = check_port_usage(443)
    if pid_443:
        process_name = get_process_name(pid_443)
        print(f"Port 443 is used by: {process_name} (PID: {pid_443})")

def stop_iis():
    """Stop IIS if it's running"""
    print("\n🔧 Stopping IIS (Internet Information Services)")
    print("-" * 50)
    
    try:
        # Stop IIS
        result = subprocess.run(['iisreset', '/stop'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ IIS stopped successfully")
        else:
            print("⚠️  IIS stop command failed or IIS not installed")
    except FileNotFoundError:
        print("ℹ️  IIS not found (this is normal if IIS is not installed)")
    except Exception as e:
        print(f"❌ Error stopping IIS: {e}")

def check_xampp_installation():
    """Check XAMPP installation"""
    print("\n🔍 Checking XAMPP Installation")
    print("-" * 50)
    
    # Common XAMPP paths
    xampp_paths = [
        r'C:\xampp',
        r'C:\XAMPP',
        r'D:\xampp',
        r'E:\xampp'
    ]
    
    xampp_path = None
    for path in xampp_paths:
        if os.path.exists(path):
            xampp_path = path
            print(f"✅ XAMPP found at: {path}")
            break
    
    if not xampp_path:
        print("❌ XAMPP installation not found in common locations")
        return None
    
    # Check important files
    important_files = [
        'apache\\bin\\httpd.exe',
        'mysql\\bin\\mysqld.exe',
        'xampp-control.exe',
        'phpMyAdmin\\index.php'
    ]
    
    for file_path in important_files:
        full_path = os.path.join(xampp_path, file_path)
        if os.path.exists(full_path):
            print(f"✅ Found: {file_path}")
        else:
            print(f"❌ Missing: {file_path}")
    
    return xampp_path

def start_xampp_services(xampp_path):
    """Start XAMPP services"""
    if not xampp_path:
        print("❌ Cannot start services - XAMPP path not found")
        return
    
    print(f"\n🚀 Starting XAMPP Services")
    print("-" * 50)
    
    # Start Apache
    apache_exe = os.path.join(xampp_path, 'apache', 'bin', 'httpd.exe')
    if os.path.exists(apache_exe):
        try:
            print("Starting Apache...")
            subprocess.Popen([apache_exe, '-D', 'FOREGROUND'], 
                           cwd=os.path.join(xampp_path, 'apache', 'bin'))
            time.sleep(3)
            
            # Check if Apache started
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('localhost', 80))
            sock.close()
            
            if result == 0:
                print("✅ Apache started successfully")
            else:
                print("❌ Apache failed to start")
                
        except Exception as e:
            print(f"❌ Error starting Apache: {e}")

def main():
    """Main troubleshooting function"""
    print("🚀 XAMPP Apache & phpMyAdmin Troubleshooter")
    print("=" * 60)
    
    # Step 1: Check current service status
    check_xampp_services()
    
    # Step 2: Check XAMPP installation
    xampp_path = check_xampp_installation()
    
    # Step 3: Stop conflicting services
    stop_iis()
    
    # Step 4: Fix port conflicts
    fix_apache_port_conflicts()
    
    # Step 5: Try to start services
    if xampp_path:
        start_xampp_services(xampp_path)
    
    print(f"\n" + "=" * 60)
    print("📋 MANUAL STEPS TO TRY:")
    print("1. Open XAMPP Control Panel as Administrator")
    print("2. Stop all services first")
    print("3. Start MySQL service")
    print("4. Start Apache service")
    print("5. Check for error messages in XAMPP logs")
    print("6. Try accessing http://localhost/phpmyadmin")
    
    print(f"\n🔧 IF APACHE STILL WON'T START:")
    print("1. Check XAMPP\\apache\\logs\\error.log for detailed errors")
    print("2. Try changing Apache port from 80 to 8080 in httpd.conf")
    print("3. Run XAMPP Control Panel as Administrator")
    print("4. Disable Windows Defender/Antivirus temporarily")

if __name__ == "__main__":
    main()
