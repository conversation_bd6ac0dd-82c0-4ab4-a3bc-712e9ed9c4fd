import mysql.connector
from mysql.connector import Error
import sys

def test_database_connection():
    """Test connection to the MySQL database"""
    
    # Database connection parameters
    config = {
        'host': '*************',
        'database': 'ivf',
        'user': 'admin',
        'password': 'Ph<PERSON>yAdmin@2025',
        'port': 3306,
        'connection_timeout': 30,
        'autocommit': True,
        'raise_on_warnings': True
    }
    
    connection = None
    
    try:
        print("Attempting to connect to MySQL database...")
        print(f"Host: {config['host']}")
        print(f"Database: {config['database']}")
        print(f"User: {config['user']}")
        print("-" * 50)
        
        # Create connection
        connection = mysql.connector.connect(**config)
        
        if connection.is_connected():
            db_info = connection.get_server_info()
            print(f"✅ Successfully connected to MySQL Server version {db_info}")
            
            # Get database info
            cursor = connection.cursor()
            cursor.execute("SELECT DATABASE();")
            database_name = cursor.fetchone()
            print(f"✅ Connected to database: {database_name[0]}")
            
            # List tables in the database
            cursor.execute("SHOW TABLES;")
            tables = cursor.fetchall()
            
            if tables:
                print(f"\n📋 Tables found in '{config['database']}' database:")
                for table in tables:
                    print(f"  - {table[0]}")
                    
                # Get table structure for each table
                print(f"\n📊 Table structures:")
                for table in tables:
                    table_name = table[0]
                    cursor.execute(f"DESCRIBE {table_name};")
                    columns = cursor.fetchall()
                    print(f"\n  Table: {table_name}")
                    print("  Columns:")
                    for column in columns:
                        print(f"    - {column[0]} ({column[1]})")
            else:
                print(f"\n⚠️  No tables found in '{config['database']}' database")
                
            cursor.close()
            
        else:
            print("❌ Failed to connect to MySQL database")
            
    except Error as e:
        print(f"❌ Error while connecting to MySQL: {e}")
        return False
        
    finally:
        if connection and connection.is_connected():
            connection.close()
            print(f"\n🔐 MySQL connection is closed")
            
    return True

if __name__ == "__main__":
    test_database_connection()
