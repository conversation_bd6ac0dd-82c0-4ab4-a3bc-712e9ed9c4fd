import os
import shutil
import re

def find_xampp_path():
    """Find <PERSON>AMPP installation path"""
    possible_paths = [
        r'C:\xampp',
        r'C:\XAMPP', 
        r'D:\xampp',
        r'E:\xampp'
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    return None

def backup_config_file(config_path):
    """Create backup of httpd.conf"""
    try:
        backup_path = config_path + '.backup'
        shutil.copy2(config_path, backup_path)
        print(f"✅ Backup created: {backup_path}")
        return True
    except Exception as e:
        print(f"❌ Failed to create backup: {e}")
        return False

def fix_httpd_conf(xampp_path):
    """Fix the httpd.conf configuration"""
    config_path = os.path.join(xampp_path, 'apache', 'conf', 'httpd.conf')
    
    if not os.path.exists(config_path):
        print(f"❌ httpd.conf not found at: {config_path}")
        return False
    
    print(f"🔧 Fixing Apache configuration...")
    print(f"Config file: {config_path}")
    
    # Create backup
    if not backup_config_file(config_path):
        return False
    
    try:
        # Read the configuration file
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Fix the DocumentRoot issue
        # Replace the problematic DocumentRoot with the correct one
        content = re.sub(
            r'DocumentRoot\s+["\']?C:/xampp/htdocs/nammakovai/?["\']?',
            'DocumentRoot "C:/xampp/htdocs"',
            content,
            flags=re.IGNORECASE
        )
        
        # Also fix any Directory directives that might reference the same path
        content = re.sub(
            r'<Directory\s+["\']?C:/xampp/htdocs/nammakovai/?["\']?>',
            '<Directory "C:/xampp/htdocs">',
            content,
            flags=re.IGNORECASE
        )
        
        # Write the fixed configuration back
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Apache configuration fixed!")
        print("   - DocumentRoot set to C:/xampp/htdocs")
        print("   - Directory directive updated")
        
        return True
        
    except Exception as e:
        print(f"❌ Error fixing configuration: {e}")
        return False

def check_htdocs_directory(xampp_path):
    """Check if htdocs directory exists and is accessible"""
    htdocs_path = os.path.join(xampp_path, 'htdocs')
    
    print(f"\n🔍 Checking htdocs directory...")
    
    if os.path.exists(htdocs_path):
        print(f"✅ htdocs directory exists: {htdocs_path}")
        
        # Check if it's readable
        try:
            files = os.listdir(htdocs_path)
            print(f"✅ htdocs is readable ({len(files)} items found)")
            
            # Check for important files
            important_files = ['index.php', 'phpmyadmin']
            for item in important_files:
                item_path = os.path.join(htdocs_path, item)
                if os.path.exists(item_path):
                    print(f"✅ Found: {item}")
                else:
                    print(f"⚠️  Missing: {item}")
                    
        except Exception as e:
            print(f"❌ htdocs directory is not readable: {e}")
            return False
    else:
        print(f"❌ htdocs directory does not exist: {htdocs_path}")
        return False
    
    return True

def test_apache_config(xampp_path):
    """Test Apache configuration"""
    apache_exe = os.path.join(xampp_path, 'apache', 'bin', 'httpd.exe')
    
    if not os.path.exists(apache_exe):
        print(f"❌ Apache executable not found: {apache_exe}")
        return False
    
    print(f"\n🧪 Testing Apache configuration...")
    
    try:
        import subprocess
        result = subprocess.run([apache_exe, '-t'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Apache configuration test passed!")
            return True
        else:
            print("❌ Apache configuration test failed:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        return False

def main():
    """Main function to fix Apache configuration"""
    print("🚀 Apache Configuration Fixer")
    print("=" * 50)
    
    # Find XAMPP installation
    xampp_path = find_xampp_path()
    if not xampp_path:
        print("❌ XAMPP installation not found!")
        print("Please make sure XAMPP is installed in a standard location.")
        return
    
    print(f"✅ XAMPP found at: {xampp_path}")
    
    # Check htdocs directory
    if not check_htdocs_directory(xampp_path):
        print("❌ htdocs directory issues found")
        return
    
    # Fix httpd.conf
    if fix_httpd_conf(xampp_path):
        # Test the configuration
        if test_apache_config(xampp_path):
            print(f"\n🎉 SUCCESS! Apache configuration has been fixed.")
            print(f"\nNext steps:")
            print(f"1. Open XAMPP Control Panel as Administrator")
            print(f"2. Start Apache service")
            print(f"3. Start MySQL service") 
            print(f"4. Try accessing http://localhost")
            print(f"5. Try accessing http://localhost/phpmyadmin")
        else:
            print(f"\n⚠️  Configuration fixed but test failed.")
            print(f"Please check XAMPP logs for more details.")
    else:
        print(f"\n❌ Failed to fix Apache configuration")

if __name__ == "__main__":
    main()
