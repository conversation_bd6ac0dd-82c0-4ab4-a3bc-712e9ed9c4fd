import mysql.connector
from mysql.connector import Error
import socket
import telnetlib
import sys

def test_network_connectivity():
    """Test basic network connectivity to the server"""
    host = '*************'
    port = 3306
    
    print("🔍 Testing network connectivity...")
    print(f"Host: {host}")
    print(f"Port: {port}")
    print("-" * 50)
    
    # Test socket connection
    try:
        print("Testing socket connection...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ Port {port} is open and accessible")
            return True
        else:
            print(f"❌ Port {port} is not accessible (error code: {result})")
            return False
            
    except Exception as e:
        print(f"❌ Socket test failed: {e}")
        return False

def test_telnet_connection():
    """Test telnet connection to MySQL port"""
    host = '*************'
    port = 3306
    
    print("\n🔍 Testing telnet connection...")
    try:
        tn = telnetlib.Telnet()
        tn.open(host, port, timeout=10)
        print(f"✅ Telnet connection to {host}:{port} successful")
        tn.close()
        return True
    except Exception as e:
        print(f"❌ Telnet connection failed: {e}")
        return False

def test_mysql_connection_variations():
    """Test MySQL connection with different configurations"""
    host = '*************'
    database = 'ivf'
    user = 'admin'
    password = 'PhpmyAdmin@2025'
    
    print("\n🔍 Testing MySQL connection variations...")
    print("-" * 50)
    
    # Test different configurations
    configs = [
        {
            'name': 'Standard connection',
            'config': {
                'host': host,
                'database': database,
                'user': user,
                'password': password,
                'port': 3306,
                'connection_timeout': 30
            }
        },
        {
            'name': 'Connection without database',
            'config': {
                'host': host,
                'user': user,
                'password': password,
                'port': 3306,
                'connection_timeout': 30
            }
        },
        {
            'name': 'Connection with SSL disabled',
            'config': {
                'host': host,
                'database': database,
                'user': user,
                'password': password,
                'port': 3306,
                'connection_timeout': 30,
                'ssl_disabled': True
            }
        }
    ]
    
    for test_config in configs:
        print(f"\n📋 Testing: {test_config['name']}")
        try:
            connection = mysql.connector.connect(**test_config['config'])
            if connection.is_connected():
                print(f"✅ {test_config['name']} - SUCCESS")
                
                cursor = connection.cursor()
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()
                print(f"   MySQL Version: {version[0]}")
                
                # Try to show databases
                cursor.execute("SHOW DATABASES")
                databases = cursor.fetchall()
                print(f"   Available databases: {[db[0] for db in databases]}")
                
                cursor.close()
                connection.close()
                return True
            else:
                print(f"❌ {test_config['name']} - Connection failed")
                
        except Error as e:
            print(f"❌ {test_config['name']} - Error: {e}")
        except Exception as e:
            print(f"❌ {test_config['name']} - Unexpected error: {e}")
    
    return False

def main():
    """Main test function"""
    print("🚀 Database Connection Diagnostic Tool")
    print("=" * 60)
    
    # Test 1: Network connectivity
    network_ok = test_network_connectivity()
    
    # Test 2: Telnet connection
    telnet_ok = test_telnet_connection()
    
    # Test 3: MySQL connection variations
    mysql_ok = test_mysql_connection_variations()
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY:")
    print(f"Network connectivity: {'✅ OK' if network_ok else '❌ FAILED'}")
    print(f"Telnet connection: {'✅ OK' if telnet_ok else '❌ FAILED'}")
    print(f"MySQL connection: {'✅ OK' if mysql_ok else '❌ FAILED'}")
    
    if not any([network_ok, telnet_ok, mysql_ok]):
        print("\n🔧 TROUBLESHOOTING SUGGESTIONS:")
        print("1. Check if the server IP address is correct")
        print("2. Verify that MySQL server is running on the remote host")
        print("3. Check firewall settings (port 3306 should be open)")
        print("4. Verify network connectivity to the server")
        print("5. Check if the server allows remote connections")
        print("6. Verify MySQL user permissions for remote access")

if __name__ == "__main__":
    main()
