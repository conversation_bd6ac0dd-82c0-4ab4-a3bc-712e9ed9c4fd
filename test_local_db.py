import mysql.connector
from mysql.connector import Error
import socket
import sys

def test_local_mysql_connection():
    """Test connection to local MySQL/MariaDB server"""
    
    # Common local MySQL configurations
    local_configs = [
        {
            'name': 'XAMPP Default (no password)',
            'config': {
                'host': 'localhost',
                'user': 'root',
                'password': '',
                'port': 3306
            }
        },
        {
            'name': '<PERSON>AM<PERSON> with password',
            'config': {
                'host': 'localhost',
                'user': 'root',
                'password': 'root',
                'port': 3306
            }
        },
        {
            'name': 'Local 127.0.0.1 (no password)',
            'config': {
                'host': '127.0.0.1',
                'user': 'root',
                'password': '',
                'port': 3306
            }
        },
        {
            'name': 'Local 127.0.0.1 with password',
            'config': {
                'host': '127.0.0.1',
                'user': 'root',
                'password': 'root',
                'port': 3306
            }
        }
    ]
    
    print("🔍 Testing Local MySQL/MariaDB Connections")
    print("=" * 60)
    
    # First check if port 3306 is open locally
    print("📡 Checking if MySQL port 3306 is open locally...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('localhost', 3306))
        sock.close()
        
        if result == 0:
            print("✅ Port 3306 is open on localhost")
        else:
            print("❌ Port 3306 is not accessible on localhost")
            print("   Make sure XAMPP MySQL/MariaDB is running")
            return False
    except Exception as e:
        print(f"❌ Error checking port: {e}")
        return False
    
    print("\n🔍 Testing different connection configurations...")
    print("-" * 50)
    
    successful_config = None
    
    for test_config in local_configs:
        print(f"\n📋 Testing: {test_config['name']}")
        try:
            connection = mysql.connector.connect(**test_config['config'])
            
            if connection.is_connected():
                print(f"✅ {test_config['name']} - SUCCESS!")
                
                # Get server info
                cursor = connection.cursor()
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()
                print(f"   📊 MySQL/MariaDB Version: {version[0]}")
                
                # Show databases
                cursor.execute("SHOW DATABASES")
                databases = cursor.fetchall()
                db_list = [db[0] for db in databases]
                print(f"   📋 Available databases: {db_list}")
                
                # Check if 'ivf' database exists
                if 'ivf' in db_list:
                    print("   ✅ 'ivf' database found!")
                    
                    # Connect to ivf database and show tables
                    cursor.execute("USE ivf")
                    cursor.execute("SHOW TABLES")
                    tables = cursor.fetchall()
                    
                    if tables:
                        print(f"   📋 Tables in 'ivf' database:")
                        for table in tables:
                            print(f"      - {table[0]}")
                            
                        # Show structure of each table
                        print(f"\n   📊 Table structures:")
                        for table in tables:
                            table_name = table[0]
                            cursor.execute(f"DESCRIBE {table_name}")
                            columns = cursor.fetchall()
                            print(f"\n      Table: {table_name}")
                            for column in columns:
                                print(f"        - {column[0]} ({column[1]})")
                    else:
                        print("   ⚠️  'ivf' database exists but has no tables")
                else:
                    print("   ⚠️  'ivf' database not found")
                    print("   💡 You may need to create it or import your data")
                
                cursor.close()
                connection.close()
                successful_config = test_config
                break
                
        except Error as e:
            print(f"❌ {test_config['name']} - MySQL Error: {e}")
        except Exception as e:
            print(f"❌ {test_config['name']} - Error: {e}")
    
    if successful_config:
        print(f"\n🎉 SUCCESS! Local MySQL connection working with:")
        print(f"   Host: {successful_config['config']['host']}")
        print(f"   User: {successful_config['config']['user']}")
        print(f"   Password: {'(empty)' if not successful_config['config']['password'] else '(set)'}")
        print(f"   Port: {successful_config['config']['port']}")
        return successful_config['config']
    else:
        print(f"\n❌ No successful local MySQL connections found")
        print(f"\n🔧 TROUBLESHOOTING:")
        print(f"   1. Make sure XAMPP is running")
        print(f"   2. Start MySQL/MariaDB service in XAMPP Control Panel")
        print(f"   3. Check XAMPP MySQL configuration")
        return None

def create_ivf_database_if_needed(config):
    """Create ivf database if it doesn't exist"""
    try:
        # Connect without specifying database
        connection = mysql.connector.connect(
            host=config['host'],
            user=config['user'],
            password=config['password'],
            port=config['port']
        )
        
        cursor = connection.cursor()
        
        # Check if ivf database exists
        cursor.execute("SHOW DATABASES LIKE 'ivf'")
        result = cursor.fetchone()
        
        if not result:
            print(f"\n🔧 Creating 'ivf' database...")
            cursor.execute("CREATE DATABASE ivf")
            print(f"✅ 'ivf' database created successfully!")
        else:
            print(f"\n✅ 'ivf' database already exists")
        
        cursor.close()
        connection.close()
        return True
        
    except Error as e:
        print(f"❌ Error creating database: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Local MySQL/MariaDB Connection Test")
    print("=" * 60)
    
    # Test local connections
    config = test_local_mysql_connection()
    
    if config:
        # Ask if user wants to create ivf database
        print(f"\n" + "=" * 60)
        print(f"📋 Database connection successful!")
        
        # Try to create ivf database if needed
        create_ivf_database_if_needed(config)
        
        print(f"\n💡 You can use these connection details in your application:")
        print(f"   Host: {config['host']}")
        print(f"   User: {config['user']}")
        print(f"   Password: {'(empty)' if not config['password'] else config['password']}")
        print(f"   Database: ivf")
        print(f"   Port: {config['port']}")
    else:
        print(f"\n❌ Could not establish local MySQL connection")
        print(f"   Please check XAMPP MySQL service status")

if __name__ == "__main__":
    main()
