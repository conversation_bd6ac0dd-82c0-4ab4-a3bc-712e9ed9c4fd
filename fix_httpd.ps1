# PowerShell script to fix Apache httpd.conf
$configFile = "C:\xampp\apache\conf\httpd.conf"

Write-Host "Fixing Apache httpd.conf configuration..." -ForegroundColor Yellow

# Read the file content
$content = Get-Content $configFile -Raw

# Replace the incorrect DocumentRoot
$content = $content -replace 'DocumentRoot "/xampp/htdocs/nammakovai/"', 'DocumentRoot "C:/xampp/htdocs"'

# Also check for any Directory directives
$content = $content -replace '<Directory "/xampp/htdocs/nammakovai/">', '<Directory "C:/xampp/htdocs">'

# Write the corrected content back
Set-Content -Path $configFile -Value $content -Encoding UTF8

Write-Host "Configuration fixed!" -ForegroundColor Green
Write-Host "DocumentRoot changed from '/xampp/htdocs/nammakovai/' to 'C:/xampp/htdocs'" -ForegroundColor Green

# Test the configuration
Write-Host "Testing Apache configuration..." -ForegroundColor Yellow
$apacheExe = "C:\xampp\apache\bin\httpd.exe"
if (Test-Path $apacheExe) {
    $testResult = & $apacheExe -t 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Apache configuration test PASSED!" -ForegroundColor Green
    } else {
        Write-Host "Apache configuration test FAILED:" -ForegroundColor Red
        Write-Host $testResult -ForegroundColor Red
    }
} else {
    Write-Host "Apache executable not found at $apacheExe" -ForegroundColor Red
}
